import axios from "axios";
import type { IAppService } from "./impl/IAppService";
import { VoicesService } from "./VoicesService";
import type { GenerateInput } from "../types/GenerateInput";
import type { GenerateResponse } from "../types/GenerateResponse";
import type { SelectedPresetResponse } from "../types/SelectedPresetResponse";
import { playAudioWithFallback } from "../utils/audioUtils";

// --- CONFIGURACIÓN ---
// Carga de variables de entorno para la configuración del servicio.
// Esto permite cambiar la configuración sin modificar el código.
const BASE_URL = import.meta.env.VITE_LLM_API_URL;
const API_KEY = import.meta.env.VITE_LLM_API_KEY;
const PRESET_GENCHARBOT = import.meta.env.VITE_LLM_PRESETID_GENCHARBOT;
const PRESET_IA_VS_PLAYER = import.meta.env.VITE_LLM_PRESETID_IA_VS_PLAYER;

/**
 * AppService es la clase principal que gestiona la lógica de negocio y la comunicación
 * con la API del modelo de lenguaje.
 *
 * Utiliza el patrón Singleton para asegurar que solo exista una única instancia de este servicio
 * en toda la aplicación, manteniendo un estado consistente (como el ID de sesión).
 */
export class AppService implements IAppService {
  private static instance: AppService;
  private sesid = "";
  private selectedPreset = PRESET_GENCHARBOT;
  private voicesService: VoicesService;
  private audioGeneratedCallback?: (audioUrl: string) => void;
  private audioFinishedCallback?: () => void;
  private audioStartedCallback?: () => void;

  private constructor() {
    this.voicesService = VoicesService.getInstance();
  }

  static getInstance(): AppService {
    if (!AppService.instance) {
      AppService.instance = new AppService();
      AppService.instance.init();
    }
    return AppService.instance;
  }

  private async init(): Promise<void> {
    const existingSessId = localStorage.getItem("sessid");

    if (existingSessId) {
      this.sesid = existingSessId;
      return;
    }

    try {
      const response = await this.selectPreset();
      this.sesid = response.sesid;
      localStorage.setItem("sessid", this.sesid);
    } catch (error) {
      console.error("Error al inicializar el servicio:", error);
    }
  }

  /**
   * Establece manualmente el ID de sesión.
   * @param id El nuevo ID de sesión.
   */
  setSesid(id: string): void {
    this.sesid = id;
  }

  /**
   * Establece el preset a utilizar
   * @param preset "gencharbot" | "ia_vs_player"
   * @returns void
   */
  async setPreset(preset: "gencharbot" | "ia_vs_player"): Promise<void> {
    switch (preset) {
      case "gencharbot":
        this.selectedPreset = PRESET_GENCHARBOT;
        break;
      case "ia_vs_player":
        this.selectedPreset = PRESET_IA_VS_PLAYER;
        break;
      default:
        console.warn(`Preset '${preset}' no reconocido.`);
        return;
    }

    return this.updatePreset();
  }

  /**
   * Llama a la API para actualizar el preset en el servidor y obtener un nuevo `sesid`
   * asociado a esa configuración.
   */
  private async updatePreset(): Promise<void> {
    try {
      const response = await this.selectPreset();
      this.sesid = response.sesid;
    } catch (error) {
      console.error("Error al actualizar el preset:", error);
    }
  }

  /**
   * Construye y devuelve las cabeceras HTTP necesarias para las peticiones a la API.
   * @returns Un objeto con las cabeceras.
   */
  private getHeaders(): Record<string, string> {
    return {
      "Access-Control-Allow-Origin": "*",
      "Content-Type": "application/json",
      "X-Api-Key": API_KEY || "",
      "X-MG-Ses": this.sesid,
      "X-Frame-Options": "SAMEORIGIN",
    };
  }

  /**
   * Un manejador genérico para todas las peticiones de Axios.
   * Centraliza el logging y la gestión de errores.
   * @param request La promesa de la petición de Axios.
   * @returns La data de la respuesta.
   */
  private async handleRequest<T>(request: Promise<any>): Promise<T> {
    try {
      const res = await request;
      if (import.meta.env.MODE === "development") {
        console.log(`🟢 ${res.status} ${res.config.url}`, res.data);
      }
      return res.data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        if (error instanceof Error) {
          console.error(`🔴 RESPONSE ERROR: ${error.message}`);
        } else {
          console.error("🔴 RESPONSE ERROR: Unknown error", error);
        }
      }
      throw new Error("Error al realizar la solicitud");
    }
  }

  /**
   * Envía un texto (query) a la API para obtener una respuesta generada por el LLM.
   * @param text El texto o pregunta del usuario.
   * @param id Un ID opcional para la consulta.
   * @returns Una promesa con la respuesta de la API.
   */
  async generate(text: string, id?: string): Promise<GenerateResponse> {
    let data: GenerateInput = {
      sesid: this.sesid,
      preset: this.selectedPreset,
      query: text,
      query_args: {},
    };

    if (id) {
      data.query_args.query_id = id;
    }

    return this.handleRequest<GenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );
  }

  async selectPreset(): Promise<SelectedPresetResponse> {
    return this.handleRequest<SelectedPresetResponse>(
      axios.get(`${BASE_URL}preset/${this.selectedPreset}`, {
        headers: this.getHeaders(),
      })
    );
  }

  // Método para configurar el callback de audio
  setAudioCallback(callback: (audioUrl: string) => void): void {
    this.audioGeneratedCallback = callback;
  }

  setAudioStartedCallback(callback: () => void): void {
    this.audioStartedCallback = callback;
  }

  // Método para configurar el callback de audio finalizado
  setAudioFinishedCallback(callback: () => void): void {
    this.audioFinishedCallback = callback;
  }

  // Método para obtener el callback de audio finalizado
  getAudioFinishedCallback(): (() => void) | undefined {
    return this.audioFinishedCallback;
  }

  // Método privado para generar audio automáticamente
  private async generateAudioForResponse(responseText: string): Promise<void> {
    try {
      console.log("🔊 Configurando servicio de voces...");

      // Asegurar que el servicio de voces esté configurado
      const voiceConfigured = await this.voicesService.configVoice("neutral");
      if (!voiceConfigured) {
        console.warn("⚠️ No se pudo configurar el servicio de voces");
        // ✅ CORREGIDO: Ejecutar callback incluso si falla la configuración
        if (this.audioFinishedCallback) {
          setTimeout(() => {
            this.audioFinishedCallback!();
          }, 1000);
        }
        return;
      }

      console.log("🔊 Generando audio para:", responseText);
      const audioBlob = await this.voicesService.getAudio(responseText);
      const audioUrl = URL.createObjectURL(audioBlob);

      // ✅ CORREGIDO: Usar la función importada directamente
      if (this.audioGeneratedCallback) {
        this.audioGeneratedCallback(audioUrl);

        // ✅ CORREGIDO: Llamar directamente sin require
        setTimeout(() => {
          playAudioWithFallback(
            audioUrl,
            this.audioFinishedCallback, // onEnded
            this.audioStartedCallback, // onStarted
            (error: string) => {
              // onError
              console.error("❌ Error reproduciendo audio:", error);
              // Ejecutar callback de finalización en caso de error
              if (this.audioFinishedCallback) {
                setTimeout(() => {
                  this.audioFinishedCallback!();
                }, 1000);
              }
            }
          );
        }, 100);
      }

      console.log("✅ Audio generado exitosamente");
    } catch (error) {
      console.error("❌ Error generando audio:", error);
      // ✅ MEJORADO: Siempre ejecutar callback de finalización
      if (this.audioFinishedCallback) {
        setTimeout(() => {
          this.audioFinishedCallback!();
        }, 2000);
      }
    }
  }

  /**
   *
   * @param response
   * @returns
   */
  private async processIaVsPlayerResponse(
    response: GenerateResponse
  ): Promise<GenerateResponse> {
    try {
      console.log(
        "🔍 Respuesta cruda de la API:",
        JSON.stringify(response, null, 2)
      );

      // Buscar el campo que contiene la respuesta - ORDEN CORREGIDO
      let responseText =
        response.output || // ✅ Primero output (es el campo principal según la API)
        response.response ||
        response.result ||
        response.text ||
        response.content;

      console.log("📝 Texto extraído:", responseText);

      // Siempre generar audio para cualquier respuesta de texto
      if (responseText && typeof responseText === "string") {
        let audioText = "";
        let displayText = "";

        // Si la respuesta es un string que parece JSON, parsearlo
        if (responseText.trim().startsWith("{")) {
          try {
            const parsedResponse = JSON.parse(responseText);
            console.log("🎮 Respuesta parseada del juego:", parsedResponse);

            if (parsedResponse.respuesta) {
              audioText = parsedResponse.respuesta;

              // Formatear la respuesta para mostrar
              displayText = `🤖 IA: ${parsedResponse.respuesta}`;

              if (parsedResponse.pista) {
                displayText += `\n💡 Pista: ${parsedResponse.pista}`;
              }

              if (parsedResponse.cuenta_regresiva !== undefined) {
                displayText += `\n⏰ Preguntas restantes: ${parsedResponse.cuenta_regresiva}`;
              }

              if (parsedResponse.acertado) {
                displayText += `\n🎉 ¡La IA ha acertado!`;
              }

              if (parsedResponse.juego_finalizado) {
                displayText += `\n🏁 Juego finalizado`;
              }
            }
          } catch (parseError) {
            console.error("Error parseando JSON:", parseError);
            // Si no se puede parsear, usar el texto original
            audioText = responseText;
            displayText = responseText;
          }
        } else {
          // Para respuestas de texto simple (como el saludo inicial)
          audioText = responseText;
          displayText = responseText;
        }

        // SIEMPRE generar audio si hay texto
        if (audioText) {
          console.log("🔊 Generando audio para:", audioText);
          await this.generateAudioForResponse(audioText);
        }

        // Devolver la respuesta con el texto formateado
        return {
          ...response,
          response: displayText || responseText,
        };
      }

      // Si no hay texto, devolver la respuesta original con mensaje de debug
      console.warn("⚠️ No se encontró texto en la respuesta:", response);
      return {
        ...response,
        response:
          "Debug: Respuesta sin texto - " +
          JSON.stringify(response, null, 2).substring(0, 100),
      };
    } catch (error) {
      console.error("Error procesando respuesta:", error);
      return {
        ...response,
        response:
          "Error procesando respuesta: " +
          (error instanceof Error ? error.message : String(error)),
      };
    }
  }

  // Métodos específicos para cada preset con diferentes comportamientos
  async generateWithGenCharBot(
    text: string,
    id?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("gencharbot");
    return this.generate(text, id);
  }

  /**
   * Genera una respuesta utilizando el preset IA vs Player
   * @param text
   * @param id
   * @param personaje
   * @returns
   */
  async generateWithIaVsPlayer(
    text: string,
    id?: string,
    personaje?: string
  ): Promise<GenerateResponse> {
    await this.setPreset("ia_vs_player");

    console.log("📝 Personaje recibido:", personaje);
    console.log("📝 Texto recibido:", text);

    // Include character info directly in query text
    let finalQuery = text;
    if (personaje) {
      finalQuery = `PERSONAJE A ADIVINAR: ${personaje}\n\n${text}`;
      console.log("📤 Query con personaje incluido:", finalQuery);
    }

    const data: GenerateInput = {
      sesid: this.sesid,
      preset: this.selectedPreset,
      query: finalQuery,
      query_args: {},
    };

    if (id) {
      data.query_args.query_id = id;
    }

    console.log("📤 Datos finales:", JSON.stringify(data, null, 2));

    const response = await this.handleRequest<GenerateResponse>(
      axios.post(`${BASE_URL}generate`, data, { headers: this.getHeaders() })
    );

    return this.processIaVsPlayerResponse(response);
  }

  // Método adicional para debuggear la configuración del preset
  async debugPresetConfiguration(): Promise<void> {
    try {
      console.log("🔍 Obteniendo configuración del preset...");

      const response = await axios.get(
        `${BASE_URL}preset/${this.selectedPreset}`,
        {
          headers: this.getHeaders(),
        }
      );

      console.log(
        "📋 Configuración completa del preset:",
        JSON.stringify(response.data, null, 2)
      );

      // Específicamente buscar campos requeridos
      if (response.data.preset?.template) {
        console.log("📝 Template del preset:", response.data.preset.template);
      }

      if (response.data.preset?.preamble) {
        console.log("📋 Preamble del preset:", response.data.preset.preamble);
      }
    } catch (error) {
      console.error("❌ Error obteniendo configuración del preset:", error);
    }
  }

  getCurrentPreset(): string {
    return this.selectedPreset;
  }

  // Testing

  // Método para probar diferentes formatos de envío
}
