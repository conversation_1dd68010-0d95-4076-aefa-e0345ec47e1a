// Configuración centralizada de variables de entorno
export const config = {
  // Configuración de la API de IA
  llm: {
    apiUrl: import.meta.env.VITE_LLM_API_URL,
    apiKey: import.meta.env.VITE_LLM_API_KEY,
    presets: {
      genCharBot: import.meta.env.VITE_LLM_PRESETID_GENCHARBOT,
      iaVsPlayer: import.meta.env.VITE_LLM_PRESETID_IA_VS_PLAYER,
    }
  },
  
  // Configuración de Speech API
  speech: {
    apiUrl: import.meta.env.VITE_SPEECH_API_URL,
    apiKey: import.meta.env.VITE_SPEECH_API_KEY,
  },
  
  // Configuración de Movistar Plus API
  movistar: {
    apiUrl: import.meta.env.VITE_MOVISTAR_API_URL,
  },
  
  // Configuración Perplexity
  perplexity: {
    id: import.meta.env.VITE_PERPLEXITY_ID,
  }
};

// Función para validar que todas las variables necesarias estén configuradas
export const validateConfig = () => {
  const errors: string[] = [];
  
  if (!config.llm.apiUrl) errors.push('VITE_LLM_API_URL no está configurada');
  if (!config.llm.apiKey) errors.push('VITE_LLM_API_KEY no está configurada');
  if (!config.llm.presets.genCharBot) errors.push('VITE_LLM_PRESETID_GENCHARBOT no está configurada');
  if (!config.llm.presets.iaVsPlayer) errors.push('VITE_LLM_PRESETID_IA_VS_PLAYER no está configurada');
  if (!config.speech.apiUrl) errors.push('VITE_SPEECH_API_URL no está configurada');
  if (!config.speech.apiKey) errors.push('VITE_SPEECH_API_KEY no está configurada');
  
  return {
    isValid: errors.length === 0,
    errors
  };
};
